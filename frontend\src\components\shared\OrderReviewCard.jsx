import Link from "next/link"
import getStatusConfig from "@/utils/statusConfig";
import { cn } from "@/lib/utils";

export default function OrderReviewCard({ order }) {
    const statusConfig = getStatusConfig(order.status);
    const StatusIcon = statusConfig.icon;

    return (
        <Link href={`/orders/${order.id}`}>
            <div className="bg-white rounded-2xl overflow-hidden mt-2">
                <div className="bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200">
                    <div className="flex items-center justify-between">
                        <h3 className="text-lg font-semibold text-gray-900">
                            Заказ #{order.id}
                        </h3>
                        <div className={cn(
                            "inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border",
                            statusConfig.className
                        )}>
                            <StatusIcon className="w-4 h-4" />
                            {statusConfig.label}
                        </div>
                    </div>
                </div>
            </div>
        </Link>
    )
}