{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 14, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/Section.jsx"], "sourcesContent": ["import { cn } from \"@/lib/utils\";\nimport Container from \"./Container\";\n\nexport default function Section({ \n  children, \n  className,\n  containerClassName,\n  containerSize = \"default\",\n  spacing = \"default\",\n  background = \"transparent\",\n  as: Component = \"section\",\n  ...props \n}) {\n  const spacingVariants = {\n    none: \"\",\n    sm: \"py-8 md:py-12\",\n    default: \"py-12 md:py-16 lg:py-20\",\n    lg: \"py-16 md:py-20 lg:py-24\",\n    xl: \"py-20 md:py-24 lg:py-32\"\n  };\n\n  const backgroundVariants = {\n    transparent: \"\",\n    white: \"bg-white\",\n    gray: \"bg-gray-50\",\n    primary: \"bg-primary/5\",\n    muted: \"bg-muted\"\n  };\n\n  return (\n    <Component\n      className={cn(\n        // Базовые стили секции\n        \"relative w-full\",\n        \n        // Отступы\n        spacingVariants[spacing],\n        \n        // Фон\n        backgroundVariants[background],\n        \n        className\n      )}\n      {...props}\n    >\n      <Container \n        size={containerSize}\n        className={containerClassName}\n      >\n        {children}\n      </Container>\n    </Component>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;AAEe,SAAS,QAAQ,EAC9B,QAAQ,EACR,SAAS,EACT,kBAAkB,EAClB,gBAAgB,SAAS,EACzB,UAAU,SAAS,EACnB,aAAa,aAAa,EAC1B,IAAI,YAAY,SAAS,EACzB,GAAG,OACJ;IACC,MAAM,kBAAkB;QACtB,MAAM;QACN,IAAI;QACJ,SAAS;QACT,IAAI;QACJ,IAAI;IACN;IAEA,MAAM,qBAAqB;QACzB,aAAa;QACb,OAAO;QACP,MAAM;QACN,SAAS;QACT,OAAO;IACT;IAEA,qBACE,8OAAC;QACC,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,uBAAuB;QACvB,mBAEA,UAAU;QACV,eAAe,CAAC,QAAQ,EAExB,MAAM;QACN,kBAAkB,CAAC,WAAW,EAE9B;QAED,GAAG,KAAK;kBAET,cAAA,8OAAC,yIAAA,CAAA,UAAS;YACR,MAAM;YACN,WAAW;sBAEV;;;;;;;;;;;AAIT", "debugId": null}}, {"offset": {"line": 63, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/layout/index.js"], "sourcesContent": ["export { default as Container } from './Container';\nexport { default as Section } from './Section';\nexport { default as Header } from './Header';\n"], "names": [], "mappings": ";AAAA;AACA;AACA", "debugId": null}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/utils/statusConfig.js"], "sourcesContent": ["import { Triangle<PERSON>lert, CheckCircle } from \"lucide-react\";\r\n\r\nexport default function getStatusConfig(status) {\r\n    const configs = {\r\n        new: {\r\n            icon: <PERSON><PERSON>lert,\r\n            label: \"Новый\",\r\n            className: \"bg-yellow-100 text-yellow-800 border-yellow-200\"\r\n        },\r\n        confirmed: {\r\n            icon: CheckCircle,\r\n            label: \"Подтвержден\",\r\n            className: \"bg-blue-100 text-blue-800 border-blue-200\"\r\n        },\r\n        completed: {\r\n            icon: CheckCircle,\r\n            label: \"Выполнен\",\r\n            className: \"bg-green-100 text-green-800 border-green-200\"\r\n        },\r\n        cancelled: {\r\n            icon: TriangleAlert,\r\n            label: \"Отменен\",\r\n            className: \"bg-red-100 text-red-800 border-red-200\"\r\n        }\r\n    };\r\n    return configs[status] || configs.new;\r\n}"], "names": [], "mappings": ";;;AAAA;AAAA;;AAEe,SAAS,gBAAgB,MAAM;IAC1C,MAAM,UAAU;QACZ,KAAK;YACD,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,WAAW;QACf;QACA,WAAW;YACP,MAAM,wNAAA,CAAA,gBAAa;YACnB,OAAO;YACP,WAAW;QACf;IACJ;IACA,OAAO,OAAO,CAAC,OAAO,IAAI,QAAQ,GAAG;AACzC", "debugId": null}}, {"offset": {"line": 126, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/shared/OrderReviewCard.jsx"], "sourcesContent": ["import Link from \"next/link\"\r\nimport getStatusConfig from \"@/utils/statusConfig\";\r\nimport { cn } from \"@/lib/utils\";\r\n\r\nexport default function OrderReviewCard({ order }) {\r\n    const statusConfig = getStatusConfig(order.status);\r\n    const StatusIcon = statusConfig.icon;\r\n\r\n    return (\r\n        <Link href={`/orders/${order.id}`}>\r\n            <div className=\"bg-white rounded-2xl overflow-hidden mt-2\">\r\n                <div className=\"bg-gradient-to-r from-gray-50 to-gray-100 px-6 py-4 border-b border-gray-200\">\r\n                    <div className=\"flex items-center justify-between\">\r\n                        <h3 className=\"text-lg font-semibold text-gray-900\">\r\n                            Заказ #{order.id}\r\n                        </h3>\r\n                        <div className={cn(\r\n                            \"inline-flex items-center gap-2 px-3 py-1.5 rounded-full text-sm font-medium border\",\r\n                            statusConfig.className\r\n                        )}>\r\n                            <StatusIcon className=\"w-4 h-4\" />\r\n                            {statusConfig.label}\r\n                        </div>\r\n                    </div>\r\n                </div>\r\n            </div>\r\n        </Link>\r\n    )\r\n}"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;;;;;AAEe,SAAS,gBAAgB,EAAE,KAAK,EAAE;IAC7C,MAAM,eAAe,CAAA,GAAA,4HAAA,CAAA,UAAe,AAAD,EAAE,MAAM,MAAM;IACjD,MAAM,aAAa,aAAa,IAAI;IAEpC,qBACI,8OAAC,4JAAA,CAAA,UAAI;QAAC,MAAM,CAAC,QAAQ,EAAE,MAAM,EAAE,EAAE;kBAC7B,cAAA,8OAAC;YAAI,WAAU;sBACX,cAAA,8OAAC;gBAAI,WAAU;0BACX,cAAA,8OAAC;oBAAI,WAAU;;sCACX,8OAAC;4BAAG,WAAU;;gCAAsC;gCACxC,MAAM,EAAE;;;;;;;sCAEpB,8OAAC;4BAAI,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACb,sFACA,aAAa,SAAS;;8CAEtB,8OAAC;oCAAW,WAAU;;;;;;gCACrB,aAAa,KAAK;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAO/C", "debugId": null}}, {"offset": {"line": 203, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/accordion.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx <module evaluation>\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,iEACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,iEACA", "debugId": null}}, {"offset": {"line": 227, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/accordion.jsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server\";\nexport const Accordion = registerClientReference(\n    function() { throw new Error(\"Attempted to call Accordion() from the server but Accordion is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"Accordion\",\n);\nexport const AccordionContent = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionContent() from the server but AccordionContent is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionContent\",\n);\nexport const AccordionItem = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionItem() from the server but AccordionItem is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionItem\",\n);\nexport const AccordionTrigger = registerClientReference(\n    function() { throw new Error(\"Attempted to call AccordionTrigger() from the server but AccordionTrigger is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/src/components/ui/accordion.jsx\",\n    \"AccordionTrigger\",\n);\n"], "names": [], "mappings": ";;;;;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA;AAEG,MAAM,gBAAgB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAC/C;IAAa,MAAM,IAAI,MAAM;AAA0O,GACvQ,6CACA;AAEG,MAAM,mBAAmB,CAAA,GAAA,6OAAA,CAAA,0BAAuB,AAAD,EAClD;IAAa,MAAM,IAAI,MAAM;AAAgP,GAC7Q,6CACA", "debugId": null}}, {"offset": {"line": 251, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 259, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/badge.jsx"], "sourcesContent": ["import * as React from \"react\"\r\nimport { Slot } from \"@radix-ui/react-slot\"\r\nimport { cva } from \"class-variance-authority\";\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nconst badgeVariants = cva(\r\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\r\n  {\r\n    variants: {\r\n      variant: {\r\n        default:\r\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\r\n        secondary:\r\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\r\n        destructive:\r\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\r\n        outline:\r\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\r\n      },\r\n    },\r\n    defaultVariants: {\r\n      variant: \"default\",\r\n    },\r\n  }\r\n)\r\n\r\nfunction Badge({\r\n  className,\r\n  variant,\r\n  asChild = false,\r\n  ...props\r\n}) {\r\n  const Comp = asChild ? Slot : \"span\"\r\n\r\n  return (\r\n    <Comp\r\n      data-slot=\"badge\"\r\n      className={cn(badgeVariants({ variant }), className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport { Badge, badgeVariants }\r\n"], "names": [], "mappings": ";;;;;AAAA;AACA;AACA;AAEA;;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OACJ;IACC,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAEf", "debugId": null}}, {"offset": {"line": 305, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/components/ui/card.jsx"], "sourcesContent": ["import * as React from \"react\"\r\n\r\nimport { cn } from \"@/lib/utils\"\r\n\r\nfunction Card({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card\"\r\n      className={cn(\r\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardHeader({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-header\"\r\n      className={cn(\r\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardTitle({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-title\"\r\n      className={cn(\"leading-none font-semibold\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardDescription({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-description\"\r\n      className={cn(\"text-muted-foreground text-sm\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardAction({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-action\"\r\n      className={cn(\r\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\r\n        className\r\n      )}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nfunction CardContent({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (<div data-slot=\"card-content\" className={cn(\"px-6\", className)} {...props} />);\r\n}\r\n\r\nfunction CardFooter({\r\n  className,\r\n  ...props\r\n}) {\r\n  return (\r\n    <div\r\n      data-slot=\"card-footer\"\r\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\r\n      {...props} />\r\n  );\r\n}\r\n\r\nexport {\r\n  Card,\r\n  CardHeader,\r\n  CardFooter,\r\n  CardTitle,\r\n  CardAction,\r\n  CardDescription,\r\n  CardContent,\r\n}\r\n"], "names": [], "mappings": ";;;;;;;;;;AAAA;AAEA;;;;AAEA,SAAS,KAAK,EACZ,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,UAAU,EACjB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,gBAAgB,EACvB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAEf;AAEA,SAAS,YAAY,EACnB,SAAS,EACT,GAAG,OACJ;IACC,qBAAQ,8OAAC;QAAI,aAAU;QAAe,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QAAa,GAAG,KAAK;;;;;;AACnF;AAEA,SAAS,WAAW,EAClB,SAAS,EACT,GAAG,OACJ;IACC,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,mHAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAEf", "debugId": null}}, {"offset": {"line": 402, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/coding/vkus-vostoka-statuses/frontend/src/app/page.js"], "sourcesContent": ["import { Section } from \"@/components/layout\";\r\nimport OrderReviewCard from \"@/components/shared/OrderReviewCard\";\r\nimport { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from \"@/components/ui/accordion\";\r\nimport { Badge } from \"@/components/ui/badge\";\r\nimport { Card, CardHeader, CardTitle, CardDescription } from \"@/components/ui/card\";\r\nimport { Clock, CheckCircle, Truck, Package } from \"lucide-react\";\r\n\r\nexport default function Home() {\r\n  // Преобразованные данные заказа для компонента OrderItem\r\n  const orderData = {\r\n    id: \"**********\",\r\n    user: {\r\n      first_name: \"<PERSON>а<PERSON><PERSON>\",\r\n      phone: \"+7 (950) 079-32-65\"\r\n    },\r\n    delivery_type: \"Самовывоз\",\r\n    comment: \"Тестовый заказ, не пробовать\",\r\n    address: \"\", // Пустой для самовывоза\r\n    paymentsystem: \"banktransfer\",\r\n    status: \"new\", // Добавляем статус по умолчанию\r\n    amount: \"3150\",\r\n    meals: [\r\n      {\r\n        id: \"ehnOzTB06KH0dpL2HiZP\",\r\n        name: \"Окрошка на Квасе\",\r\n        quantity: 1400,\r\n        amount: 1400,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal2\",\r\n        name: \"Борщ украинский\",\r\n        quantity: 700,\r\n        amount: 700,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      },\r\n      {\r\n        id: \"meal3\",\r\n        name: \"Плов узбекский\",\r\n        quantity: 1050,\r\n        amount: 1050,\r\n        img: \"https://static.tildacdn.com/stor3233-3161-4831-b432-************/55155502.jpg\",\r\n        pack_m: \"350\",\r\n        price: \"1\",\r\n        unit: \"г\",\r\n        portion: \"350\"\r\n      }\r\n    ]\r\n  };\r\n\r\n  const statusSections = [\r\n    {\r\n      id: \"item-1\",\r\n      title: \"Новые заказы\",\r\n      count: 3,\r\n      icon: Clock,\r\n      color: \"text-orange-600\",\r\n      bgColor: \"bg-orange-50\",\r\n      borderColor: \"border-orange-200\"\r\n    },\r\n    {\r\n      id: \"item-2\",\r\n      title: \"Подтверждены\",\r\n      count: 10,\r\n      icon: CheckCircle,\r\n      color: \"text-blue-600\",\r\n      bgColor: \"bg-blue-50\",\r\n      borderColor: \"border-blue-200\"\r\n    },\r\n    {\r\n      id: \"item-3\",\r\n      title: \"Готовы\",\r\n      count: 2,\r\n      icon: Package,\r\n      color: \"text-green-600\",\r\n      bgColor: \"bg-green-50\",\r\n      borderColor: \"border-green-200\"\r\n    },\r\n    {\r\n      id: \"item-4\",\r\n      title: \"В пути\",\r\n      count: 80,\r\n      icon: Truck,\r\n      color: \"text-purple-600\",\r\n      bgColor: \"bg-purple-50\",\r\n      borderColor: \"border-purple-200\"\r\n    }\r\n  ];\r\n\r\n  return (\r\n    <Section spacing=\"sm\">\r\n      <Accordion\r\n        type=\"multiple\"\r\n        className=\"space-y-4\"\r\n      >\r\n        {statusSections.map((section) => {\r\n          const IconComponent = section.icon;\r\n          return (\r\n            <AccordionItem\r\n              key={section.id}\r\n              value={section.id}\r\n              className={`border rounded-xl ${section.borderColor} ${section.bgColor} transition-all duration-200`}\r\n            >\r\n              <AccordionTrigger className={`text-lg font-semibold px-6 py-4 ${section.color} hover:no-underline`}>\r\n                <div className=\"flex items-center gap-3\">\r\n                  <IconComponent className=\"w-5 h-5\" />\r\n                  <span>{section.title}</span>\r\n                  <Badge variant=\"secondary\" className=\"ml-2 font-mono\">\r\n                    {section.count}\r\n                  </Badge>\r\n                </div>\r\n              </AccordionTrigger>\r\n              <AccordionContent className=\"px-6 pb-4\">\r\n                <div className=\"space-y-3\">\r\n                  <OrderReviewCard order={orderData} />\r\n                  <OrderReviewCard order={orderData} />\r\n                  <OrderReviewCard order={orderData} />\r\n                </div>\r\n              </AccordionContent>\r\n            </AccordionItem>\r\n          );\r\n        })}\r\n      </Accordion>\r\n    </Section>\r\n  );\r\n}\r\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AAAA;AAAA;AAAA;;;;;;;;AAEe,SAAS;IACtB,yDAAyD;IACzD,MAAM,YAAY;QAChB,IAAI;QACJ,MAAM;YACJ,YAAY;YACZ,OAAO;QACT;QACA,eAAe;QACf,SAAS;QACT,SAAS;QACT,eAAe;QACf,QAAQ;QACR,QAAQ;QACR,OAAO;YACL;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;YACA;gBACE,IAAI;gBACJ,MAAM;gBACN,UAAU;gBACV,QAAQ;gBACR,KAAK;gBACL,QAAQ;gBACR,OAAO;gBACP,MAAM;gBACN,SAAS;YACX;SACD;IACH;IAEA,MAAM,iBAAiB;QACrB;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,2NAAA,CAAA,cAAW;YACjB,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,wMAAA,CAAA,UAAO;YACb,OAAO;YACP,SAAS;YACT,aAAa;QACf;QACA;YACE,IAAI;YACJ,OAAO;YACP,OAAO;YACP,MAAM,oMAAA,CAAA,QAAK;YACX,OAAO;YACP,SAAS;YACT,aAAa;QACf;KACD;IAED,qBACE,8OAAC,6KAAA,CAAA,UAAO;QAAC,SAAQ;kBACf,cAAA,8OAAC,qIAAA,CAAA,YAAS;YACR,MAAK;YACL,WAAU;sBAET,eAAe,GAAG,CAAC,CAAC;gBACnB,MAAM,gBAAgB,QAAQ,IAAI;gBAClC,qBACE,8OAAC,qIAAA,CAAA,gBAAa;oBAEZ,OAAO,QAAQ,EAAE;oBACjB,WAAW,CAAC,kBAAkB,EAAE,QAAQ,WAAW,CAAC,CAAC,EAAE,QAAQ,OAAO,CAAC,4BAA4B,CAAC;;sCAEpG,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,WAAW,CAAC,gCAAgC,EAAE,QAAQ,KAAK,CAAC,mBAAmB,CAAC;sCAChG,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAc,WAAU;;;;;;kDACzB,8OAAC;kDAAM,QAAQ,KAAK;;;;;;kDACpB,8OAAC,iIAAA,CAAA,QAAK;wCAAC,SAAQ;wCAAY,WAAU;kDAClC,QAAQ,KAAK;;;;;;;;;;;;;;;;;sCAIpB,8OAAC,qIAAA,CAAA,mBAAgB;4BAAC,WAAU;sCAC1B,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,+IAAA,CAAA,UAAe;wCAAC,OAAO;;;;;;kDACxB,8OAAC,+IAAA,CAAA,UAAe;wCAAC,OAAO;;;;;;kDACxB,8OAAC,+IAAA,CAAA,UAAe;wCAAC,OAAO;;;;;;;;;;;;;;;;;;mBAjBvB,QAAQ,EAAE;;;;;YAsBrB;;;;;;;;;;;AAIR", "debugId": null}}]}