import { Section } from "@/components/layout";
import OrderReviewCard from "@/components/shared/OrderReviewCard";
import { Accordion, AccordionItem, AccordionTrigger, AccordionContent } from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { Card, CardHeader, CardTitle, CardDescription } from "@/components/ui/card";
import { Clock, CheckCircle, Truck, Package } from "lucide-react";

export default function Home() {
  // Преобразованные данные заказа для компонента OrderItem
  const orderData = {
    id: "**********",
    user: {
      first_name: "<PERSON>а<PERSON><PERSON>",
      phone: "+7 (950) 079-32-65"
    },
    delivery_type: "Самовывоз",
    comment: "Тестовый заказ, не пробовать",
    address: "", // Пустой для самовывоза
    paymentsystem: "banktransfer",
    status: "new", // Добавляем статус по умолчанию
    amount: "3150",
    meals: [
      {
        id: "ehnOzTB06KH0dpL2HiZP",
        name: "Окрошка на Квасе",
        quantity: 1400,
        amount: 1400,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal2",
        name: "Борщ украинский",
        quantity: 700,
        amount: 700,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      },
      {
        id: "meal3",
        name: "Плов узбекский",
        quantity: 1050,
        amount: 1050,
        img: "https://static.tildacdn.com/stor3233-3161-4831-b432-************/********.jpg",
        pack_m: "350",
        price: "1",
        unit: "г",
        portion: "350"
      }
    ]
  };

  const statusSections = [
    {
      id: "item-1",
      title: "Новые заказы",
      count: 3,
      icon: Clock,
      color: "text-orange-600",
      bgColor: "bg-orange-50",
      borderColor: "border-orange-200"
    },
    {
      id: "item-2",
      title: "Подтверждены",
      count: 10,
      icon: CheckCircle,
      color: "text-blue-600",
      bgColor: "bg-blue-50",
      borderColor: "border-blue-200"
    },
    {
      id: "item-3",
      title: "Готовы",
      count: 2,
      icon: Package,
      color: "text-green-600",
      bgColor: "bg-green-50",
      borderColor: "border-green-200"
    },
    {
      id: "item-4",
      title: "В пути",
      count: 80,
      icon: Truck,
      color: "text-purple-600",
      bgColor: "bg-purple-50",
      borderColor: "border-purple-200"
    }
  ];

  return (
    <Section spacing="sm">
      <Accordion
        type="multiple"
        className="space-y-4"
      >
        {statusSections.map((section) => {
          const IconComponent = section.icon;
          return (
            <AccordionItem
              key={section.id}
              value={section.id}
              className={`border rounded-xl ${section.borderColor} ${section.bgColor} transition-all duration-200`}
            >
              <AccordionTrigger className={`text-lg font-semibold px-6 py-4 ${section.color} hover:no-underline`}>
                <div className="flex items-center gap-3">
                  <IconComponent className="w-5 h-5" />
                  <span>{section.title}</span>
                  <Badge variant="secondary" className="ml-2 font-mono">
                    {section.count}
                  </Badge>
                </div>
              </AccordionTrigger>
              <AccordionContent className="px-6 pb-4">
                <div className="space-y-3">
                  <OrderReviewCard order={orderData} />
                  <OrderReviewCard order={orderData} />
                  <OrderReviewCard order={orderData} />
                </div>
              </AccordionContent>
            </AccordionItem>
          );
        })}
      </Accordion>
    </Section>
  );
}
